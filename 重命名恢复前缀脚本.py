import os
import re

# 文件夹路径
folder_path = r"G:\素材测试"

# 支持的视频和文本后缀
valid_exts = ['.mp4', '.avi', '.mov', '.mkv', '.txt']

# 遍历文件夹
for filename in os.listdir(folder_path):
    name, ext = os.path.splitext(filename)

    # 跳过不相关文件
    if ext.lower() not in valid_exts:
        continue

    # 匹配形如 10001_LLC_Positive_001 的格式
    match = re.match(r"\d+_[^_]+(?:_[^_]+)*_(\d+)", name)
    if match:
        original_name = f"{match.group(1)}{ext}"
        old_path = os.path.join(folder_path, filename)
        new_path = os.path.join(folder_path, original_name)
        os.rename(old_path, new_path)
        print(f"✅ 重命名：{filename} → {original_name}")

print("🎉 所有文件已恢复为原始命名！")
