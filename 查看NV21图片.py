import cv2
import numpy as np

# 推荐使用原始字符串，防止路径报错
file_path = r"C:\Users\<USER>\Desktop\eis1080P3_1920x1080.NV21"

# 图像分辨率
width = 1920
height = 1080

# 读取 NV21 原始数据
with open(file_path, "rb") as f:
    nv21_data = f.read()

# 转为 numpy 数组并重构为 YUV 图像格式
nv21 = np.frombuffer(nv21_data, dtype=np.uint8)
yuv_image = nv21.reshape((height * 3 // 2, width))

# 转为 BGR 图像（OpenCV 可显示）
bgr_image = cv2.cvtColor(yuv_image, cv2.COLOR_YUV2BGR_NV21)

# 显示图像
cv2.imshow("NV21 Image", bgr_image)
cv2.waitKey(0)
cv2.destroyAllWindows()
